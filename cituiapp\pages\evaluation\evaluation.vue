<template>
	<view class="page-container">
		<!-- 固定顶部区域 -->
		<view class="fixed-header">
			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-container">
					<input
						v-model="searchValue"
						type="text"
						placeholder="搜索APP"
						class="search-input"
						@confirm="handleSearch"
						@input="handleSearchInput"
					/>
					<view v-if="!isSearching" class="search-icon" @click="handleSearch">
						<view class="icon-search"></view>
					</view>
					<view v-else class="clear-icon" @click="clearSearch">
						<view class="icon-clear">×</view>
					</view>
				</view>
			</view>
			
			<!-- 标签导航 -->
			<view class="tab-section">
				<view class="tab-container">
					<view
						v-for="(tab, index) in visibleTabs"
						:key="index"
						class="tab-item"
						:class="{ 'active': activeTab === index }"
						@click="switchTab(index)"
					>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
				</view>
				
				<!-- 筛选按钮 -->
				<view class="filter-btn" @click="showFilterPopup">
					<text class="filter-text">筛选</text>
					<text class="filter-icon">▽</text>
				</view>
			</view>
			
			<!-- 提交评测报告按钮 (仅管理员可见) -->
			<view v-if="showSubmitButton" class="submit-section">
				<button class="submit-btn" @click="handleSubmitReport">
					<text class="submit-icon">⊕</text>
					<text class="submit-text">提交评测报告赚取积分</text>
				</button>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-container" :style="{ paddingTop: contentPaddingTop }">
			<scroll-view 
				class="scroll-container"
				scroll-y
				refresher-enabled
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
				@scrolltolower="onLoadMore"
				lower-threshold="100"
			>
				<view class="content-inner">
					<!-- 今日放水推荐 -->
					<!-- <view class="recommendation-section">
						<view class="recommendation-header">
							<text class="fire-icon">🔥</text>
							<text class="recommendation-title">今日放水推荐</text>
						</view>
						<view class="recommendation-list">
							<text class="recommendation-item">《金币大师》新用户+8元（11-13点）</text>
							<text class="recommendation-item">《短剧星球》看剧双倍收益</text>
							<text class="recommendation-item">《阅读赚》今日签到翻倍</text>
						</view>
					</view> -->

					<!-- 标题区域 -->
					<view v-if="isSearching" class="section-title">
						<text class="title-text">搜索结果：{{ searchKeyword }}</text>
					</view>

					<!-- 评测卡片列表 -->
					<view class="card-list">
						<view 
							v-for="(item, index) in evaluationList" 
							:key="item.id"
							class="evaluation-card"
							:class="getCardGradientClass(index)"
							@click="handleCardClick(item)"
						>
							<!-- 卡片头部 -->
							<view class="card-header">
								<view class="app-info">
									<view class="app-icon" :class="getIconClass(item.type)">
										<text class="icon-symbol">{{ getIconSymbol(item.type) }}</text>
									</view>
									<view class="app-details">
										<text class="app-name">{{ item.name }}</text>
										<text class="update-time">{{ item.updateTime }}</text>
									</view>
								</view>
								<view class="app-stats">
									<view class="rating">
										<view class="star-icon">⭐</view>
										<text class="rating-text">{{ item.rating }}</text>
									</view>
									<view class="download-count">
										<view class="download-icon">⬇</view>
										<text class="count-text">{{ item.downloadCount }}次</text>
									</view>
								</view>
							</view>
							
							<!-- 标签区域 -->
							<view class="tags-container">
								<view 
									v-for="tag in item.tags" 
									:key="tag.text"
									class="tag"
									:class="getTagClass(tag.type)"
								>
									<text class="tag-text">{{ tag.emoji }}{{ tag.text }}</text>
								</view>
							</view>
							
							<!-- 卡片底部 -->
							<view class="card-footer">
								<view class="action-btn" @click.stop="handleAction(item)">
									<text class="btn-text">{{ getActionBtnText(item.status) }}</text>
								</view>
								<view class="detail-btn" @click.stop="handleDetail(item)">
									<text class="detail-text">查看完整报告</text>
									<text class="arrow">›</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 加载更多提示 -->
					<view class="load-more-container" v-if="showLoadMore">
						<u-loadmore 
							:status="loadStatus" 
							:load-text="loadText"
							margin-top="20"
							margin-bottom="20"
						/>
					</view>
					
				</view>
			</scroll-view>
		</view>
		
		<!-- 筛选弹窗 -->
		<u-popup 
			:show="filterVisible" 
			mode="bottom" 
			:border-radius="20"
			@close="hideFilterPopup"
		>
			<view class="filter-popup">
				<view class="filter-header">
					<text class="filter-popup-title">筛选条件</text>
					<view class="filter-close" @click="hideFilterPopup">
						<text class="close-icon">✕</text>
					</view>
				</view>
				
				<view class="filter-content">
					<!-- APP类型 -->
					<view class="filter-group">
						<text class="filter-group-title">APP类型</text>
						<view class="filter-tags">
							<view 
								v-for="(type, index) in appTypes" 
								:key="index"
								class="filter-tag"
								:class="{ 'active': selectedAppTypes.includes(type) }"
								@click="toggleAppType(type)"
							>
								<text class="filter-tag-text">{{ type }}</text>
							</view>
						</view>
					</view>
					
					<!-- 运行模式 -->
					<view class="filter-group">
						<text class="filter-group-title">运行模式</text>
						<view class="filter-tags">
							<view 
								v-for="(mode, index) in runModes" 
								:key="index"
								class="filter-tag"
								:class="{ 'active': selectedRunModes.includes(mode) }"
								@click="toggleRunMode(mode)"
							>
								<text class="filter-tag-text">{{ mode }}</text>
							</view>
						</view>
					</view>
					
					<!-- 新人福利 -->
					<view class="filter-group">
						<text class="filter-group-title">新人福利</text>
						<view class="filter-tags">
							<view 
								v-for="(benefit, index) in newUserBenefits" 
								:key="index"
								class="filter-tag"
								:class="{ 'active': selectedBenefits.includes(benefit) }"
								@click="toggleBenefit(benefit)"
							>
								<text class="filter-tag-text">{{ benefit }}</text>
							</view>
						</view>
					</view>
					
					<!-- 提现门槛 -->
					<view class="filter-group">
						<text class="filter-group-title">提现门槛</text>
						<view class="filter-tags">
							<view 
								v-for="(threshold, index) in withdrawThresholds" 
								:key="index"
								class="filter-tag"
								:class="{ 'active': selectedThresholds.includes(threshold) }"
								@click="toggleThreshold(threshold)"
							>
								<text class="filter-tag-text">{{ threshold }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="filter-actions">
					<button class="reset-btn" @click="resetFilter">重置</button>
					<button class="confirm-btn" @click="confirmFilter">确定</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { isLoggedIn, isAdmin, getCurrentUser } from '@/utils/storage.js'

	export default {
		data() {
			return {
				searchValue: '',
				isSearching: false, // 是否处于搜索状态
				searchKeyword: '', // 当前搜索关键词
				activeTab: 0,
				tabs: [
					{ name: '最新评测' },
					{ name: '我的', needLogin: true }
				],
				isRefreshing: false,
				showLoadMore: false,
				loadStatus: 'loadmore',
				loadText: {
					loadmore: '点击或上拉加载更多',
					loading: '正在加载...',
					nomore: '没有更多了'
				},
				currentPage: 1,
				pageSize: 10,
				filterVisible: false,
				showSubmitButton: false, // 控制提交按钮显示
				
				// 筛选选项
				appTypes: ['全部', '合成游戏', '短剧', '阅读', '走路','答题','其它'],
				runModes: ['全部', '自动', '手动'],
				newUserBenefits: ['全部', '≥1元', '0.5-0.99元', '＜0.5元'],
				withdrawThresholds: ['全部', '≤0.1元', '0.1-1元', '＞1元'],
				
				// 当前选择的筛选条件
				selectedAppTypes: ['全部'],
				selectedRunModes: ['全部'],
				selectedBenefits: ['全部'],
				selectedThresholds: ['全部'],

				evaluationList: []
			}
		},

		computed: {
			// 检查是否显示提交按钮
			shouldShowSubmitButton() {
				return isLoggedIn() && isAdmin()
			},

			// 动态计算内容区域的padding-top
			contentPaddingTop() {
				return this.showSubmitButton ? '300rpx' : '210rpx'
			},

			// 可见的标签列表
			visibleTabs() {
				if (this.showSubmitButton) {
					// 如果显示提交按钮，显示所有标签
					return this.tabs
				} else {
					// 否则只显示第一个标签（最新评测）
					return this.tabs.filter((_, index) => index === 0)
				}
			}
		},

		methods: {
			// 切换标签
			switchTab(index) {
				const tab = this.tabs[index]

				// 如果是"我的"标签，需要验证登录状态
				if (tab.needLogin && !isLoggedIn()) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/login/login'
						})
					}, 1500)
					return
				}

				this.activeTab = index
				this.loadMoreData(true)
			},
			
			// 显示筛选弹窗
			showFilterPopup() {
				this.filterVisible = true
			},
			
			// 隐藏筛选弹窗
			hideFilterPopup() {
				this.filterVisible = false
			},
			
			// 切换APP类型筛选
			toggleAppType(type) {
				if (type === '全部') {
					this.selectedAppTypes = ['全部']
				} else {
					const index = this.selectedAppTypes.indexOf(type)
					if (index > -1) {
						this.selectedAppTypes.splice(index, 1)
					} else {
						this.selectedAppTypes.push(type)
					}
					// 移除"全部"选项
					const allIndex = this.selectedAppTypes.indexOf('全部')
					if (allIndex > -1) {
						this.selectedAppTypes.splice(allIndex, 1)
					}
					// 如果没有选择任何选项，默认选择"全部"
					if (this.selectedAppTypes.length === 0) {
						this.selectedAppTypes = ['全部']
					}
				}
			},
			
			// 切换运行模式筛选
			toggleRunMode(mode) {
				if (mode === '全部') {
					this.selectedRunModes = ['全部']
				} else {
					const index = this.selectedRunModes.indexOf(mode)
					if (index > -1) {
						this.selectedRunModes.splice(index, 1)
					} else {
						this.selectedRunModes.push(mode)
					}
					const allIndex = this.selectedRunModes.indexOf('全部')
					if (allIndex > -1) {
						this.selectedRunModes.splice(allIndex, 1)
					}
					if (this.selectedRunModes.length === 0) {
						this.selectedRunModes = ['全部']
					}
				}
			},
			
			// 切换新人福利筛选
			toggleBenefit(benefit) {
				if (benefit === '全部') {
					this.selectedBenefits = ['全部']
				} else {
					const index = this.selectedBenefits.indexOf(benefit)
					if (index > -1) {
						this.selectedBenefits.splice(index, 1)
					} else {
						this.selectedBenefits.push(benefit)
					}
					const allIndex = this.selectedBenefits.indexOf('全部')
					if (allIndex > -1) {
						this.selectedBenefits.splice(allIndex, 1)
					}
					if (this.selectedBenefits.length === 0) {
						this.selectedBenefits = ['全部']
					}
				}
			},
			
			// 切换提现门槛筛选
			toggleThreshold(threshold) {
				if (threshold === '全部') {
					this.selectedThresholds = ['全部']
				} else {
					const index = this.selectedThresholds.indexOf(threshold)
					if (index > -1) {
						this.selectedThresholds.splice(index, 1)
					} else {
						this.selectedThresholds.push(threshold)
					}
					const allIndex = this.selectedThresholds.indexOf('全部')
					if (allIndex > -1) {
						this.selectedThresholds.splice(allIndex, 1)
					}
					if (this.selectedThresholds.length === 0) {
						this.selectedThresholds = ['全部']
					}
				}
			},
			
			// 重置筛选条件
			resetFilter() {
				this.selectedAppTypes = ['全部']
				this.selectedRunModes = ['全部']
				this.selectedBenefits = ['全部']
				this.selectedThresholds = ['全部']
			},
			
			// 确认筛选
			confirmFilter() {
				this.filterVisible = false
				this.loadMoreData(true)
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				setTimeout(() => {
					this.currentPage = 1
					this.loadMoreData(true)
					this.isRefreshing = false
				}, 1500)
			},
			
			// 刷新恢复
			onRestore() {
				this.isRefreshing = false
			},
			
			// 上滑加载更多
			onLoadMore() {
				if (this.loadStatus !== 'loading') {
					this.loadMoreData()
				}
			},
			
			// 加载更多数据
			loadMoreData(isRefresh = false) {
				this.loadStatus = 'loading'
				this.showLoadMore = true

				// 如果是刷新，重置页码
				if (isRefresh) {
					this.currentPage = 1
				} else {
					// 非刷新情况下，先递增页码
					this.currentPage++
				}

				// 构建请求参数
				const params = {
					page: this.currentPage,
					page_size: this.pageSize
				}

				// 如果有搜索关键词，添加搜索条件
				if (this.searchValue.trim()) {
					params.search = this.searchValue.trim()
				}

				// 如果是"我的"标签，添加用户ID筛选
				if (this.activeTab === 1) { // "我的"标签的索引是1
					const currentUser = getCurrentUser()
					if (currentUser && currentUser.id) {
						params.user_id = currentUser.id
					}
				}

				// 添加筛选条件
				if (!this.selectedAppTypes.includes('全部')) {
					params.app_types = this.selectedAppTypes
				}
				if (!this.selectedRunModes.includes('全部')) {
					params.run_modes = this.selectedRunModes
				}
				if (!this.selectedBenefits.includes('全部')) {
					params.benefits = this.selectedBenefits
				}
				if (!this.selectedThresholds.includes('全部')) {
					params.thresholds = this.selectedThresholds
				}

				// 调用真实API
				uni.$u.http.get('/evaluation/list', {params: params}).then(res => {
					console.log('评测列表API返回数据:', res)

					if (isRefresh) {
						this.evaluationList = res.list || []
					} else {
						this.evaluationList = [...this.evaluationList, ...(res.list || [])]
					}

					// 更新分页状态
					if (res.has_more) {
						this.loadStatus = 'loadmore'
						this.showLoadMore = true
					} else {
						this.loadStatus = 'nomore'
						// 如果有数据则显示"没有更多了"，否则隐藏
						this.showLoadMore = this.evaluationList.length > 0
					}
					console.log('当前页码:', this.currentPage, '加载状态:', this.loadStatus);

				}).catch(err => {
					console.error('加载评测数据失败:', err)
					// 请求失败时恢复页码
					if (!isRefresh) {
						this.currentPage--
					}
					this.loadStatus = 'loadmore'
					this.showLoadMore = this.evaluationList.length > 0
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					})
				})
			},
			

			
			// 获取卡片渐变样式
			getCardGradientClass(index) {
				const gradients = ['gradient-red', 'gradient-blue', 'gradient-green', 'gradient-amber', 'gradient-purple']
				return gradients[index % 5]
			},
			
			// 获取图标样式
			getIconClass(type) {
				const iconClasses = {
					coin: 'icon-coin',
					video: 'icon-video', 
					cash: 'icon-cash',
					book: 'icon-book',
					game: 'icon-game'
				}
				return iconClasses[type] || 'icon-coin'
			},
			
			// 获取图标符号
			getIconSymbol(type) {
				const symbols = {
					coin: '💰',
					video: '📺',
					cash: '💵',
					book: '📚',
					game: '🎮'
				}
				return symbols[type] || '💰'
			},
			
			// 获取标签样式
			getTagClass(type) {
				return `tag-${type}`
			},
			

			
			// 获取操作按钮文本
			getActionBtnText(status) {
				const texts = {
					'not-downloaded': '下载赚钱',
					'downloaded': '重新安装',
					'installed': '打开赚钱'
				}
				return texts[status] || '下载赚钱'
			},
			
			// 卡片点击
			handleCardClick(item) {
				console.log('卡片点击:', item.name)
				// 跳转到详情页
			},
			
			// 操作按钮点击
			handleAction(item) {
				uni.showToast({
					title: `${this.getActionBtnText(item.status)}: ${item.name}`,
					icon: 'none'
				})
			},
			
			// 详情按钮点击
			handleDetail(item) {
				// 跳转到详情页面，传递APP信息
				uni.navigateTo({
					url: `/pages/detail/detail?id=${item.id}&name=${encodeURIComponent(item.name)}`
				})
			},
			
			// 提交评测报告
			handleSubmitReport() {
				uni.navigateTo({
					url: '/pages/submit-report/submit-report'
				})
			},

			// 处理搜索输入
			handleSearchInput(e) {
				// 如果搜索框被清空，恢复到非搜索状态
				if (!e.detail.value.trim()) {
					this.clearSearch()
				}
			},

			// 执行搜索
			handleSearch() {
				const keyword = this.searchValue.trim()
				if (!keyword) {
					this.clearSearch()
					return
				}

				this.isSearching = true
				this.searchKeyword = keyword
				this.currentPage = 1
				this.loadMoreData(true)
			},

			// 清空搜索
			clearSearch() {
				this.isSearching = false
				this.searchKeyword = ''
				this.searchValue = ''
				this.currentPage = 1
				this.loadMoreData(true)
			}
		},
		
		onLoad() {
			// 检查权限并设置提交按钮显示状态
			this.showSubmitButton = this.shouldShowSubmitButton
			// 初始加载
			this.loadMoreData(true)
		},

		onShow() {
			// 页面显示时重新检查权限（用户可能在其他页面登录/退出）
			this.showSubmitButton = this.shouldShowSubmitButton
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 固定顶部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	padding: 24rpx 0 16rpx;
	
	/* 搜索框区域 */
	.search-section {
		padding: 0 32rpx;
		margin-bottom: 16rpx;
		
		.search-container {
			position: relative;
			width: 100%;
			
			.search-input {
				background-color: #f3f4f6;
				border-radius: 50rpx;
				padding: 16rpx 80rpx 16rpx 32rpx;
				font-size: 28rpx;
				border: none;
				outline: none;
				color: #111827;
				width: 100%;
				box-sizing: border-box;
				min-height: 80rpx;
				
				&::placeholder {
					color: #9ca3af;
				}
				
				/* 移除默认的input样式 */
				&:focus {
					outline: none;
					border: none;
				}
			}
			
			.search-icon, .clear-icon {
				position: absolute;
				right: 24rpx;
				top: 50%;
				transform: translateY(-50%);
				cursor: pointer;

				.icon-search {
					width: 32rpx;
					height: 32rpx;
					background-color: #6b7280;
					mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5l-1.5 1.5l-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16A6.5 6.5 0 0 1 3 9.5A6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z'/%3E%3C/svg%3E") no-repeat center;
					mask-size: contain;
				}

				.icon-clear {
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #9ca3af;
					border-radius: 50%;
					color: #ffffff;
					font-size: 24rpx;
					font-weight: bold;
				}
			}
		}
	}
	
	/* 标签导航区域 */
	.tab-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx 16rpx;
		
		.tab-container {
			display: flex;
			
			.tab-item {
				margin-right: 48rpx;
				padding-bottom: 16rpx;
				position: relative;
				
				.tab-text {
					font-size: 32rpx;
					color: #6b7280;
					font-weight: 500;
				}
				
				&.active {
					.tab-text {
						color: #111827;
						font-weight: bold;
					}
					
					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						height: 4rpx;
						background-color: #ef4444;
						border-radius: 2rpx;
					}
				}
			}
		}
		
		.filter-btn {
			display: flex;
			align-items: center;
			background-color: #f3f4f6;
			padding: 12rpx 24rpx;
			border-radius: 50rpx;
			
			.filter-text {
				font-size: 24rpx;
				color: #6b7280;
				margin-right: 8rpx;
			}
			
			.filter-icon {
				font-size: 24rpx;
			}
		}
	}
	
	/* 提交按钮区域 */
	.submit-section {
		padding: 0 32rpx;
		
		.submit-btn {
			width: 100%;
			background: linear-gradient(135deg, #ff9500, #ff8c00);
			border-radius: 16rpx;
			padding: 18rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none;
			box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
			
			.submit-icon {
				font-size: 28rpx;
				color: #ffffff;
				margin-right: 12rpx;
				font-weight: bold;
			}
			
			.submit-text {
				font-size: 26rpx;
				color: #ffffff;
				font-weight: 600;
			}
		}
	}
}

/* 内容区域 */
.content-container {
	/* padding-top 通过动态样式绑定设置 */
	height: 100vh;
	background-color: #f9fafb;
	
	.scroll-container {
		height: 100%;
		
		.content-inner {
			padding: 32rpx;

			/* 标题样式 */
			.section-title {
				margin-bottom: 24rpx;

				.title-text {
					font-size: 36rpx; /* text-lg */
					font-weight: bold;
					color: #111827;
				}
			}

			/* 今日放水推荐 */
			.recommendation-section {
				background: linear-gradient(135deg, #fff8e1, #ffecb3);
				border: 1rpx solid rgba(255, 193, 7, 0.3);
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 32rpx;
				box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.15);
				
				.recommendation-header {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;
					
					.fire-icon {
						font-size: 28rpx;
						margin-right: 8rpx;
					}
					
					.recommendation-title {
						font-size: 28rpx;
						font-weight: bold;
						color: #e65100;
					}
				}
				
				.recommendation-list {
					padding-left: 0;
					
					.recommendation-item {
						display: block;
						font-size: 24rpx;
						color: #5d4037;
						margin-bottom: 6rpx;
						line-height: 1.5;
						padding-left: 16rpx;
						position: relative;
						
						&:before {
							content: '•';
							color: #e65100;
							font-weight: bold;
							position: absolute;
							left: 0;
						}
					}
				}
			}
			
			/* 评测卡片列表 */
			.card-list {
				.evaluation-card {
					background: #ffffff;
					border-radius: 16rpx;
					padding: 32rpx;
					margin-bottom: 32rpx;
					box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
					position: relative;
					overflow: hidden;
					
					/* 渐变背景 */
					&.gradient-red {
						background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
					}
					
					&.gradient-blue {
						background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
					}
					
					&.gradient-green {
						background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
					}
					
					&.gradient-amber {
						background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
					}
					
					&.gradient-purple {
						background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
					}
					
					/* 卡片头部 */
					.card-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 24rpx;
						
						.app-info {
							display: flex;
							align-items: center;
							
							.app-icon {
								width: 48rpx;
								height: 48rpx;
								border-radius: 8rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-right: 16rpx;
								
								&.icon-coin {
									background-color: #f59e0b;
								}
								
								&.icon-video {
									background-color: #ef4444;
								}
								
								&.icon-cash {
									background-color: #10b981;
								}
								
								&.icon-book {
									background-color: #3b82f6;
								}
								
								&.icon-game {
									background-color: #8b5cf6;
								}
								
								.icon-symbol {
									font-size: 22rpx;
								}
							}
							
							.app-details {
								flex: 1;
								
								.app-name {
									display: block;
									font-size: 32rpx;
									font-weight: 600;
									color: #111827;
									margin-bottom: 4rpx;
								}
								
								.update-time {
									display: block;
									font-size: 22rpx;
									color: #9ca3af;
									font-weight: 400;
								}
							}
						}
						
						.app-stats {
							display: flex;
							gap: 32rpx;
							
							.rating, .download-count {
								display: flex;
								align-items: center;
								
								.star-icon, .download-icon {
									font-size: 24rpx;
									margin-right: 4rpx;
								}
								
								.rating-text, .count-text {
									font-size: 24rpx;
									color: #6b7280;
								}
							}
							
							.rating .rating-text {
								color: #f59e0b;
							}
						}
					}
					
					/* 标签区域 */
					.tags-container {
						display: flex;
						flex-wrap: wrap;
						gap: 16rpx;
						margin-bottom: 24rpx;
						
						.tag {
							border-radius: 24rpx;
							padding: 4rpx 12rpx;
							display: inline-flex;
							align-items: center;
							justify-content: center;
							min-height: 44rpx;
							
							.tag-text {
								font-size: 22rpx;
								font-weight: 500;
								line-height: 1;
							}
							
							&.tag-blue {
								background-color: #dbeafe;
								
								.tag-text {
									color: #2563eb;
								}
							}
							
							&.tag-gray {
								background-color: #f3f4f6;
								
								.tag-text {
									color: #4b5563;
								}
							}
							
							&.tag-green {
								background-color: #dcfce7;
								
								.tag-text {
									color: #16a34a;
								}
							}
							
							&.tag-red {
								background-color: #fee2e2;
								
								.tag-text {
									color: #dc2626;
								}
							}
							
							&.tag-amber {
								background-color: #fef3c7;
								
								.tag-text {
									color: #d97706;
								}
							}
						}
					}
					
					/* 卡片底部 */
					.card-footer {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.action-btn {
							background: linear-gradient(135deg, #ef4444, #dc2626);
							border-radius: 24rpx;
							padding: 12rpx 24rpx;
							border: none;
							
							.btn-text {
								color: #ffffff;
								font-size: 28rpx;
								font-weight: 600;
							}
						}
						
						.detail-btn {
							display: flex;
							align-items: center;
							
							.detail-text {
								color: #ef4444;
								font-size: 28rpx;
								font-weight: 600;
								margin-right: 8rpx;
							}
							
							.arrow {
								color: #ef4444;
								font-size: 32rpx;
								font-weight: bold;
							}
						}
					}
				}
			}
			
			/* 加载更多容器 */
			.load-more-container {
				margin: 32rpx 0;
				padding-bottom: 120rpx;
			}
		}
	}
}

/* 筛选弹窗样式 */
.filter-popup {
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	
	.filter-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx;
		border-bottom: 2rpx solid #f0f0f0;
		
		.filter-popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #111827;
		}
		
		.filter-close {
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.close-icon {
				font-size: 32rpx;
				color: #6b7280;
			}
		}
	}
	
	.filter-content {
		padding: 32rpx;
		max-height: 60vh;
		overflow-y: auto;
		
		.filter-group {
			margin-bottom: 32rpx;
			
			.filter-group-title {
				display: block;
				font-size: 28rpx;
				font-weight: 600;
				color: #111827;
				margin-bottom: 16rpx;
			}
			
			.filter-tags {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;
				
				.filter-tag {
					background-color: #f3f4f6;
					border-radius: 24rpx;
					padding: 12rpx 24rpx;
					
					.filter-tag-text {
						font-size: 24rpx;
						color: #6b7280;
					}
					
					&.active {
						background-color: #dbeafe;
						
						.filter-tag-text {
							color: #2563eb;
						}
					}
				}
			}
		}
	}
	
	.filter-actions {
		display: flex;
		gap: 24rpx;
		padding: 32rpx;
		border-top: 2rpx solid #f0f0f0;
		
		.reset-btn, .confirm-btn {
			flex: 1;
			padding: 24rpx;
			border-radius: 16rpx;
			font-size: 28rpx;
			font-weight: 600;
			border: none;
		}
		
		.reset-btn {
			background-color: #f3f4f6;
			color: #6b7280;
		}
		
		.confirm-btn {
			background: linear-gradient(135deg, #3b82f6, #2563eb);
			color: #ffffff;
		}
	}
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
